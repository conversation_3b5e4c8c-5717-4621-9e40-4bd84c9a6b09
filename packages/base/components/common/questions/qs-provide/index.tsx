import { defineComponent } from 'vue'
import { qsFormInjectionKey } from './context'

const formProps = {
  disabled: {
    type: Boolean,
    default: undefined,
  },
}

export type FormSetupProps = ExtractPropTypes<typeof formProps>

export default defineComponent({
  name: 'QsProvide',
  props: formProps,
  setup(props, { slots }) {
    provide(qsFormInjectionKey, {
      props,
    })

    const handleClick = (event: MouseEvent) => {
      if ((event.target as HTMLElement).tagName === 'IMG') {
        // 实现图片放大逻辑
        const imgSrc = (event.target as HTMLImageElement).src
        // 放大图片
        console.log(imgSrc)
      }
    }

    return () => (
      <div onClick={handleClick}>
        {slots.default?.()}
      </div>
    )
  },
})
